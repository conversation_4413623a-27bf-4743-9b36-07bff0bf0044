#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests d'intégration complets avec un espace de test structuré.
"""

import asyncio
import os
import shutil
import tempfile
import unittest
from unittest.mock import patch

import pytest

from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig, SecretStr
from ..orchestrator import SyncOrchestrator
from ..processing import ContentRetriever
from .fixtures import (
    DataFactory,
    create_mock_confluence_client
)


class TestCompleteIntegration(unittest.TestCase):
    """Tests d'intégration complets avec données de test réalistes."""

    def setUp(self):
        """Configuration des tests."""
        # Créer un répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()

        # Configuration de test
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token=SecretStr("test_token")
        )

        self.criteria = SearchCriteria(
            spaces=["TESTSPACE"],
            types=["page", "blogpost"],
            max_results=50,
            include_attachments=True,
            include_children=True
        )

        self.storage_config = StorageConfig(
            storage_type="filesystem",
            output_dir=self.temp_dir,
            attachment_extensions_to_download_raw=['.pdf', '.docx', '.xlsx', '.png'],
            attachment_extensions_to_convert=['.txt', '.json', '.csv']
        )

        self.processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=50,
            max_parallel_downloads=3
        )

        # Créer les données de test
        self.test_factory = DataFactory()
        self.test_pages = self.test_factory.create_page_hierarchy()

        # Ajouter des pièces jointes à certaines pages
        self.test_attachments = {}
        for page in self.test_pages[:3]:  # Premières pages avec pièces jointes
            attachments = self.test_factory.create_attachments_for_page(page.id, 2)
            self.test_attachments[page.id] = attachments
            page.attachments = attachments

    def tearDown(self):
        """Nettoyage après les tests."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @pytest.mark.integration
    def test_complete_workflow_with_test_space(self):
        """Test du workflow complet avec un espace de test structuré."""
        # Créer un client mocké avec nos données de test
        mock_client = create_mock_confluence_client(self.test_pages, self.test_attachments)

        # Créer l'orchestrateur avec le client mocké
        with patch('confluence_rag.orchestrator.ConfluenceClient', return_value=mock_client):
            orchestrator = SyncOrchestrator(
                self.config,
                self.criteria,
                self.storage_config,
                self.processing_config
            )

            # Utiliser un change tracker dans le répertoire temporaire
            from ..tracking import ConfluenceChangeTracker
            orchestrator.change_tracker = ConfluenceChangeTracker(storage_dir=os.path.join(self.temp_dir, "tracking"))

            # Exécuter la synchronisation de manière synchrone
            stats = asyncio.run(orchestrator.run())

            # Vérifications
            self.assertIsNotNone(stats)
            self.assertIn("total_content_items", stats)
            self.assertIn("total_attachments", stats)
            self.assertGreater(stats["total_content_items"], 0)

            # Vérifier que les fichiers ont été créés
            contents_dir = os.path.join(self.temp_dir, "contents")
            self.assertTrue(os.path.exists(contents_dir))

            # Vérifier qu'il y a des fichiers de contenu
            content_files = [f for f in os.listdir(contents_dir) if f.endswith('.json')]
            self.assertGreater(len(content_files), 0)

    @pytest.mark.integration
    def test_page_hierarchy_structure(self):
        """Test de la structure hiérarchique des pages."""
        pages = self.test_pages

        # Vérifier qu'on a différents types de contenu
        page_types = {page.type for page in pages}
        self.assertIn("page", page_types)
        self.assertIn("blogpost", page_types)

        # Vérifier la hiérarchie
        homepage = next((p for p in pages if p.id == "homepage123"), None)
        self.assertIsNotNone(homepage)
        self.assertEqual(homepage.title, "Accueil - Documentation Technique")

        # Vérifier les pages enfants
        child_pages = [p for p in pages if p.parent_id == "homepage123"]
        self.assertGreater(len(child_pages), 0)

        # Vérifier les sous-pages
        arch_page = next((p for p in pages if p.id == "arch001"), None)
        self.assertIsNotNone(arch_page)

        arch_children = [p for p in pages if p.parent_id == "arch001"]
        self.assertGreater(len(arch_children), 0)

    @pytest.mark.integration
    def test_attachments_processing(self):
        """Test du traitement des pièces jointes."""
        # Vérifier que les pages ont des pièces jointes
        pages_with_attachments = [p for p in self.test_pages if p.attachments]
        self.assertGreater(len(pages_with_attachments), 0)

        # Vérifier les types de pièces jointes
        all_attachments = []
        for page in pages_with_attachments:
            all_attachments.extend(page.attachments)

        attachment_types = {att.media_type for att in all_attachments}
        expected_types = {
            "application/pdf",
            "image/png",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        }

        # Vérifier qu'on a au moins quelques types attendus
        self.assertTrue(len(attachment_types.intersection(expected_types)) > 0)

    @pytest.mark.integration
    def test_sync_content_retriever_with_test_data(self):
        """Test du SyncContentRetriever avec les données de test."""
        from ..processing.sync_content_retriever import SyncContentRetriever
        from ..sync_client import SyncConfluenceClient

        # Créer un mock client synchrone
        mock_client = Mock(spec=SyncConfluenceClient)
        mock_client.get_content.return_value = self.test_pages[0]
        mock_client.get_attachments.return_value = self.test_attachments

        retriever = SyncContentRetriever(
            mock_client,
            self.processing_config,
            self.storage_config
        )

        # Tester la récupération d'une page spécifique de manière synchrone
        homepage = retriever.retrieve_content("homepage123", process_attachments=True)

        self.assertIsNotNone(homepage)
        self.assertEqual(homepage.id, "homepage123")
        self.assertIsNotNone(homepage.body_plain)

        # Vérifier que les chunks ont été créés
        if hasattr(homepage, 'processed_chunks') and homepage.processed_chunks:
            self.assertGreater(len(homepage.processed_chunks), 0)

    @pytest.mark.integration
    def test_search_criteria_validation(self):
        """Test de validation des critères de recherche."""
        # Vérifier que nos critères sont valides
        self.assertEqual(self.criteria.spaces, ["TESTSPACE"])
        self.assertIn("page", self.criteria.types)
        self.assertIn("blogpost", self.criteria.types)
        self.assertTrue(self.criteria.include_attachments)

        # Tester la conversion en CQL
        cql = self.criteria.to_cql()
        self.assertIn("TESTSPACE", cql)
        self.assertIn("page", cql)

    @pytest.mark.integration
    def test_storage_configuration(self):
        """Test de la configuration de stockage."""
        # Vérifier que le répertoire de sortie existe
        self.assertTrue(os.path.exists(self.temp_dir))

        # Vérifier les extensions configurées
        self.assertIn('.pdf', self.storage_config.attachment_extensions_to_download_raw)
        self.assertIn('.txt', self.storage_config.attachment_extensions_to_convert)

    def test_test_data_factory_consistency(self):
        """Test de cohérence des données générées par la factory."""
        factory = DataFactory()

        # Tester la création d'espace
        space = factory.space
        self.assertEqual(space.key, "TESTSPACE")
        self.assertEqual(space.type, "global")

        # Tester la création de pages
        pages = factory.create_page_hierarchy()
        self.assertGreater(len(pages), 5)  # Au moins quelques pages

        # Vérifier que toutes les pages appartiennent au bon espace
        for page in pages:
            self.assertEqual(page.space.key, "TESTSPACE")

        # Tester la création de pièces jointes
        attachments = factory.create_attachments_for_page("test_page", 3)
        self.assertEqual(len(attachments), 3)

        for att in attachments:
            self.assertEqual(att.content_id, "test_page")
            self.assertIsNotNone(att.download_url)


if __name__ == '__main__':
    # Générer les fichiers d'exemple si nécessaire
    from .fixtures.generate_sample_files import generate_files
    try:
        generate_files()
    except Exception as e:
        print(f"Avertissement : Impossible de générer les fichiers d'exemple : {e}")

    unittest.main()
