#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gestion des dépréciations pour le module Confluence.
"""

import warnings
import functools
from typing import Callable, TypeVar

F = TypeVar('F', bound=Callable)


def deprecated(reason: str, replacement: str = None, version: str = None) -> Callable[[F], F]:
    """
    Décorateur pour marquer les fonctions/classes comme deprecated.
    
    Args:
        reason: Raison de la dépréciation
        replacement: Composant de remplacement recommandé
        version: Version dans laquelle la dépréciation sera effective
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            message = f"{func.__name__} is deprecated"
            if reason:
                message += f": {reason}"
            if replacement:
                message += f". Use {replacement} instead"
            if version:
                message += f". Will be removed in version {version}"
            
            warnings.warn(
                message,
                DeprecationWarning,
                stacklevel=2
            )
            return func(*args, **kwargs)
        return wrapper
    return decorator


class DeprecationManager:
    """Gestionnaire centralisé des dépréciations."""
    
    @staticmethod
    def warn_asyncio_usage():
        """Avertit de l'utilisation de composants asyncio deprecated (maintenant supprimés)."""
        warnings.warn(
            "Asyncio components have been removed. "
            "Use synchronous equivalents (SyncConfluenceClient, SyncOrchestrator, etc.) instead.",
            DeprecationWarning,
            stacklevel=3
        )
    
    @staticmethod
    def warn_old_orchestrator():
        """Avertit de l'utilisation de l'ancien orchestrateur (maintenant supprimé)."""
        warnings.warn(
            "The asyncio Orchestrator has been removed. "
            "Use SyncOrchestrator instead for better performance and simpler code.",
            DeprecationWarning,
            stacklevel=3
        )
    
    @staticmethod
    def warn_old_client():
        """Avertit de l'utilisation de l'ancien client (maintenant supprimé)."""
        warnings.warn(
            "ConfluenceClient (asyncio) has been removed. "
            "Use SyncConfluenceClient instead for better integration with kbot-load-scheduler.",
            DeprecationWarning,
            stacklevel=3
        )
    
    @staticmethod
    def warn_old_processors():
        """Avertit de l'utilisation des anciens processeurs (maintenant supprimés)."""
        warnings.warn(
            "Asyncio processors (ContentRetriever, AttachmentProcessor) have been removed. "
            "Use synchronous equivalents (SyncContentRetriever, SyncAttachmentProcessor) instead.",
            DeprecationWarning,
            stacklevel=3
        )


# Constantes de dépréciation
DEPRECATION_MESSAGES = {
    "confluence_client": {
        "message": "ConfluenceClient (asyncio) has been removed",
        "replacement": "SyncConfluenceClient",
        "reason": "Better integration with kbot-load-scheduler synchronous architecture"
    },
    "orchestrator": {
        "message": "Asyncio Orchestrator has been removed",
        "replacement": "SyncOrchestrator",
        "reason": "Simplified code and better performance with ThreadPoolExecutor"
    },
    "content_retriever": {
        "message": "ContentRetriever (asyncio) has been removed",
        "replacement": "SyncContentRetriever",
        "reason": "Unified synchronous architecture"
    },
    "attachment_processor": {
        "message": "AttachmentProcessor (asyncio) has been removed",
        "replacement": "SyncAttachmentProcessor",
        "reason": "Unified synchronous architecture"
    }
}


def get_deprecation_message(component: str) -> str:
    """
    Récupère le message de dépréciation pour un composant.
    
    Args:
        component: Nom du composant deprecated
        
    Returns:
        Message de dépréciation formaté
    """
    if component not in DEPRECATION_MESSAGES:
        return f"{component} is deprecated"
    
    info = DEPRECATION_MESSAGES[component]
    message = info["message"]
    
    if info.get("reason"):
        message += f": {info['reason']}"
    
    if info.get("replacement"):
        message += f". Use {info['replacement']} instead"
    
    message += ". Component has been removed."
    
    return message


def check_asyncio_usage():
    """
    Vérifie si des composants asyncio deprecated sont utilisés.
    
    Cette fonction peut être appelée au démarrage pour détecter
    l'utilisation de composants deprecated.
    """
    import sys
    import inspect
    
    # Vérifier les modules importés
    deprecated_modules = [
        'kbotloadscheduler.loader.confluence.client',
        'kbotloadscheduler.loader.confluence.processing.content_retriever',
        'kbotloadscheduler.loader.confluence.processing.attachment_processor'
    ]
    
    found_deprecated = []
    
    for module_name in deprecated_modules:
        if module_name in sys.modules:
            found_deprecated.append(module_name)
    
    if found_deprecated:
        warnings.warn(
            f"Deprecated asyncio modules detected: {', '.join(found_deprecated)}. "
            f"Consider migrating to synchronous equivalents.",
            DeprecationWarning,
            stacklevel=2
        )
    
    return found_deprecated


def migration_status_report():
    """
    Génère un rapport du statut de migration.
    
    Returns:
        Dict avec les informations de migration
    """
    import sys
    
    # Composants synchrones disponibles
    sync_components = [
        'kbotloadscheduler.loader.confluence.sync_client',
        'kbotloadscheduler.loader.confluence.orchestrator',
        'kbotloadscheduler.loader.confluence.processing.sync_content_retriever',
        'kbotloadscheduler.loader.confluence.processing.sync_attachment_processor'
    ]
    
    # Composants asyncio deprecated
    async_components = [
        'kbotloadscheduler.loader.confluence.client',
        'kbotloadscheduler.loader.confluence.processing.content_retriever',
        'kbotloadscheduler.loader.confluence.processing.attachment_processor'
    ]
    
    sync_loaded = [comp for comp in sync_components if comp in sys.modules]
    async_loaded = [comp for comp in async_components if comp in sys.modules]
    
    return {
        "sync_components_available": len(sync_components),
        "sync_components_loaded": len(sync_loaded),
        "async_components_loaded": len(async_loaded),
        "migration_complete": len(async_loaded) == 0 and len(sync_loaded) > 0,
        "loaded_sync_components": sync_loaded,
        "loaded_async_components": async_loaded,
        "recommendation": (
            "Migration complete - using synchronous components" 
            if len(async_loaded) == 0 and len(sync_loaded) > 0
            else "Consider migrating to synchronous components"
        )
    }


if __name__ == "__main__":
    # Script pour vérifier le statut de migration
    print("=== Confluence Module Migration Status ===")
    
    status = migration_status_report()
    
    print(f"Sync components available: {status['sync_components_available']}")
    print(f"Sync components loaded: {status['sync_components_loaded']}")
    print(f"Async components loaded: {status['async_components_loaded']}")
    print(f"Migration complete: {status['migration_complete']}")
    print(f"Recommendation: {status['recommendation']}")
    
    if status['loaded_async_components']:
        print(f"\nDeprecated components in use:")
        for comp in status['loaded_async_components']:
            print(f"  - {comp}")
    
    if status['loaded_sync_components']:
        print(f"\nSynchronous components in use:")
        for comp in status['loaded_sync_components']:
            print(f"  - {comp}")
